// EPD Manager App - 網關管理頁面

import React, { useEffect, useState } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  RefreshControl,
  TouchableOpacity,
  Alert,
  ActivityIndicator,
} from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import { useStores } from '../stores/storeStore';
import { useGateways } from '../stores/gatewayStore';
import { COLORS, SIZES } from '../utils/constants';
import { Gateway } from '../types';

export const GatewayManagementScreen: React.FC = () => {
  const [refreshing, setRefreshing] = useState(false);
  const [loading, setLoading] = useState(true);
  const { selectedStore } = useStores();
  const { gateways, fetchGateways } = useGateways();

  useEffect(() => {
    loadGateways();
  }, [selectedStore]);

  const loadGateways = async () => {
    if (!selectedStore) return;
    
    setLoading(true);
    try {
      await fetchGateways(selectedStore._id || selectedStore.id);
    } catch (error) {
      console.error('載入網關列表失敗:', error);
      Alert.alert('錯誤', '載入網關列表失敗');
    } finally {
      setLoading(false);
    }
  };

  const handleRefresh = async () => {
    setRefreshing(true);
    await loadGateways();
    setRefreshing(false);
  };

  const handleGatewayPress = (gateway: Gateway) => {
    Alert.alert(
      '網關詳情',
      `名稱: ${gateway.name}\nMAC: ${gateway.macAddress}\nIP: ${gateway.ipAddress}\n型號: ${gateway.model}\n狀態: ${gateway.status === 'online' ? '在線' : '離線'}\nWiFi固件: ${gateway.wifiFirmwareVersion}\n藍芽固件: ${gateway.btFirmwareVersion}`,
      [
        { text: '確定', style: 'default' }
      ]
    );
  };

  const getStatusColor = (status: string) => {
    return status === 'online' ? COLORS.SUCCESS : COLORS.TEXT_DISABLED;
  };

  const getStatusText = (status: string) => {
    return status === 'online' ? '在線' : '離線';
  };

  if (!selectedStore) {
    return (
      <SafeAreaView style={styles.container}>
        <View style={styles.emptyContainer}>
          <Text style={styles.emptyText}>請先選擇門店</Text>
        </View>
      </SafeAreaView>
    );
  }

  return (
    <SafeAreaView style={styles.container}>
      <ScrollView
        style={styles.scrollView}
        contentContainerStyle={styles.scrollContent}
        refreshControl={
          <RefreshControl
            refreshing={refreshing}
            onRefresh={handleRefresh}
            colors={[COLORS.PRIMARY]}
          />
        }
      >
        {/* 頭部信息 */}
        <View style={styles.header}>
          <Text style={styles.title}>網關管理</Text>
          <Text style={styles.subtitle}>
            門店：{selectedStore.name}
          </Text>
        </View>

        {/* 統計信息 */}
        <View style={styles.statsContainer}>
          <View style={styles.statCard}>
            <Text style={styles.statNumber}>{gateways.length}</Text>
            <Text style={styles.statLabel}>總網關數</Text>
          </View>
          <View style={styles.statCard}>
            <Text style={styles.statNumber}>
              {gateways.filter(g => g.status === 'online').length}
            </Text>
            <Text style={styles.statLabel}>在線</Text>
          </View>
          <View style={styles.statCard}>
            <Text style={styles.statNumber}>
              {gateways.filter(g => g.status === 'offline').length}
            </Text>
            <Text style={styles.statLabel}>離線</Text>
          </View>
        </View>

        {/* 網關列表 */}
        {loading ? (
          <View style={styles.loadingContainer}>
            <ActivityIndicator size="large" color={COLORS.PRIMARY} />
            <Text style={styles.loadingText}>載入網關列表中...</Text>
          </View>
        ) : gateways.length === 0 ? (
          <View style={styles.emptyContainer}>
            <Text style={styles.emptyText}>此門店暫無網關</Text>
            <Text style={styles.emptySubtext}>
              請使用一鍵配對功能新增網關
            </Text>
          </View>
        ) : (
          <View style={styles.gatewayList}>
            <Text style={styles.sectionTitle}>網關列表</Text>
            
            {gateways.map((gateway, index) => (
              <TouchableOpacity
                key={gateway._id || index}
                style={styles.gatewayCard}
                onPress={() => handleGatewayPress(gateway)}
                activeOpacity={0.7}
              >
                <View style={styles.gatewayCardHeader}>
                  <Text style={styles.gatewayName}>{gateway.name}</Text>
                  <View style={[
                    styles.statusBadge,
                    { backgroundColor: getStatusColor(gateway.status) }
                  ]}>
                    <Text style={styles.statusText}>
                      {getStatusText(gateway.status)}
                    </Text>
                  </View>
                </View>
                
                <View style={styles.gatewayInfo}>
                  <Text style={styles.infoText}>MAC: {gateway.macAddress}</Text>
                  <Text style={styles.infoText}>IP: {gateway.ipAddress}</Text>
                  <Text style={styles.infoText}>型號: {gateway.model}</Text>
                </View>

                <View style={styles.firmwareInfo}>
                  <Text style={styles.firmwareText}>
                    WiFi: {gateway.wifiFirmwareVersion}
                  </Text>
                  <Text style={styles.firmwareText}>
                    藍芽: {gateway.btFirmwareVersion}
                  </Text>
                </View>

                {gateway.lastSeen && (
                  <Text style={styles.lastSeenText}>
                    最後上線: {new Date(gateway.lastSeen).toLocaleString('zh-TW')}
                  </Text>
                )}
              </TouchableOpacity>
            ))}
          </View>
        )}

        {/* 底部間距 */}
        <View style={styles.bottomSpacing} />
      </ScrollView>
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: COLORS.BACKGROUND,
  },
  scrollView: {
    flex: 1,
  },
  scrollContent: {
    padding: SIZES.SPACING_MD,
  },
  header: {
    marginBottom: SIZES.SPACING_LG,
  },
  title: {
    fontSize: SIZES.FONT_SIZE_XL,
    fontWeight: 'bold',
    color: COLORS.TEXT_PRIMARY,
    marginBottom: SIZES.SPACING_XS,
  },
  subtitle: {
    fontSize: SIZES.FONT_SIZE_MD,
    color: COLORS.TEXT_SECONDARY,
  },
  statsContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginBottom: SIZES.SPACING_LG,
  },
  statCard: {
    backgroundColor: COLORS.SURFACE,
    borderRadius: SIZES.BORDER_RADIUS_MD,
    padding: SIZES.SPACING_MD,
    alignItems: 'center',
    flex: 1,
    marginHorizontal: SIZES.SPACING_XS,
    borderWidth: 1,
    borderColor: COLORS.TEXT_DISABLED,
  },
  statNumber: {
    fontSize: SIZES.FONT_SIZE_XL,
    fontWeight: 'bold',
    color: COLORS.PRIMARY,
    marginBottom: SIZES.SPACING_XS,
  },
  statLabel: {
    fontSize: SIZES.FONT_SIZE_SM,
    color: COLORS.TEXT_SECONDARY,
    textAlign: 'center',
  },
  loadingContainer: {
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: SIZES.SPACING_XL,
  },
  loadingText: {
    fontSize: SIZES.FONT_SIZE_MD,
    color: COLORS.TEXT_SECONDARY,
    marginTop: SIZES.SPACING_MD,
  },
  emptyContainer: {
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: SIZES.SPACING_XL,
  },
  emptyText: {
    fontSize: SIZES.FONT_SIZE_LG,
    color: COLORS.TEXT_SECONDARY,
    marginBottom: SIZES.SPACING_SM,
  },
  emptySubtext: {
    fontSize: SIZES.FONT_SIZE_SM,
    color: COLORS.TEXT_DISABLED,
    textAlign: 'center',
  },
  gatewayList: {
    marginBottom: SIZES.SPACING_LG,
  },
  sectionTitle: {
    fontSize: SIZES.FONT_SIZE_LG,
    fontWeight: 'bold',
    color: COLORS.TEXT_PRIMARY,
    marginBottom: SIZES.SPACING_MD,
  },
  gatewayCard: {
    backgroundColor: COLORS.SURFACE,
    borderRadius: SIZES.BORDER_RADIUS_MD,
    padding: SIZES.SPACING_MD,
    marginBottom: SIZES.SPACING_SM,
    borderWidth: 1,
    borderColor: COLORS.TEXT_DISABLED,
  },
  gatewayCardHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: SIZES.SPACING_SM,
  },
  gatewayName: {
    fontSize: SIZES.FONT_SIZE_MD,
    fontWeight: '600',
    color: COLORS.TEXT_PRIMARY,
    flex: 1,
  },
  statusBadge: {
    paddingHorizontal: SIZES.SPACING_SM,
    paddingVertical: SIZES.SPACING_XS,
    borderRadius: SIZES.BORDER_RADIUS_SM,
  },
  statusText: {
    color: COLORS.SURFACE,
    fontSize: SIZES.FONT_SIZE_XS,
    fontWeight: '600',
  },
  gatewayInfo: {
    marginBottom: SIZES.SPACING_SM,
  },
  infoText: {
    fontSize: SIZES.FONT_SIZE_SM,
    color: COLORS.TEXT_SECONDARY,
    fontFamily: 'monospace',
    marginBottom: 2,
  },
  firmwareInfo: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginBottom: SIZES.SPACING_XS,
  },
  firmwareText: {
    fontSize: SIZES.FONT_SIZE_XS,
    color: COLORS.TEXT_DISABLED,
  },
  lastSeenText: {
    fontSize: SIZES.FONT_SIZE_XS,
    color: COLORS.TEXT_DISABLED,
    fontStyle: 'italic',
  },
  bottomSpacing: {
    height: SIZES.SPACING_XL,
  },
});

export default GatewayManagementScreen;
