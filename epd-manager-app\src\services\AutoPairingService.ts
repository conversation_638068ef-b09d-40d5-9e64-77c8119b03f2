// EPD Manager App - 自動配對服務

import { Gateway, GatewayConfig, AutoPairingResult } from '../types';
import { apiService } from './ApiService';
import { webSocketService } from './WebSocketService';
import { 
  generateRandomMac, 
  generateRandomIp, 
  generateGatewayName,
  generateGatewayModel,
  generateFirmwareVersion 
} from '../utils/generators';
import { 
  GATEWAY_CONSTANTS, 
  ERROR_MESSAGES, 
  SUCCESS_MESSAGES 
} from '../utils/constants';

export class AutoPairingService {
  private isProcessing: boolean = false;

  /**
   * 一鍵自動配對新網關
   * @param storeId 門店ID
   * @param gatewayName 網關名稱（可選）
   * @param macAddress MAC地址（可選，用於藍芽配對）
   */
  async autoCreateAndConnectGateway(
    storeId: string,
    gatewayName?: string,
    macAddress?: string
  ): Promise<AutoPairingResult> {
    if (this.isProcessing) {
      return {
        success: false,
        error: '配對正在進行中，請稍候'
      };
    }

    this.isProcessing = true;

    try {
      console.log('開始自動配對流程...');

      // 步驟1: 生成網關配置
      console.log('步驟1: 生成網關配置');
      const gatewayConfig = this.generateGatewayConfig(storeId, gatewayName, macAddress);
      console.log('生成的網關配置:', gatewayConfig);

      // 步驟2: 自動創建網關
      console.log('步驟2: 調用 API 創建網關');
      const createResult = await apiService.createGateway(gatewayConfig);
      
      if (!createResult.success || !createResult.data) {
        throw new Error(createResult.error || ERROR_MESSAGES.GATEWAY_CREATION_FAILED);
      }

      const gateway = createResult.data;
      console.log('網關創建成功:', gateway);

      // 步驟3: 檢查 WebSocket 配置
      if (!gateway.websocket) {
        throw new Error('服務器未返回 WebSocket 配置信息');
      }

      console.log('WebSocket 配置詳細資訊:', JSON.stringify(gateway.websocket, null, 2));
      console.log('備註: 此WebSocket資訊需要發送給對應的網關設備');

      console.log('自動配對流程完成 - 配對成功');

      return {
        success: true,
        gateway: gateway
      };

    } catch (error: any) {
      console.error('自動配對失敗:', error);

      return {
        success: false,
        error: error.message || ERROR_MESSAGES.AUTO_PAIRING_FAILED
      };
    } finally {
      this.isProcessing = false;
    }
  }

  /**
   * 生成網關配置
   * @param storeId 門店ID
   * @param gatewayName 網關名稱（可選）
   * @param macAddress MAC地址（可選）
   */
  private generateGatewayConfig(storeId: string, gatewayName?: string, macAddress?: string): GatewayConfig {
    const config: GatewayConfig = {
      name: gatewayName || generateGatewayName(),
      macAddress: macAddress || generateRandomMac(),
      status: GATEWAY_CONSTANTS.DEFAULT_STATUS,
      model: generateGatewayModel(),
      wifiFirmwareVersion: generateFirmwareVersion(),
      btFirmwareVersion: generateFirmwareVersion(),
      ipAddress: generateRandomIp(),
      storeId: storeId,
      lastSeen: new Date()
    };

    return config;
  }

  /**
   * 重新連接到現有網關
   * @param gateway 網關對象
   * @param storeId 門店ID
   */
  async reconnectToGateway(gateway: Gateway, storeId: string): Promise<AutoPairingResult> {
    if (this.isProcessing) {
      return {
        success: false,
        error: '連接正在進行中，請稍候'
      };
    }

    this.isProcessing = true;

    try {
      console.log('重新連接到網關:', gateway.name);

      // 先斷開現有連接
      if (webSocketService.isWebSocketConnected()) {
        webSocketService.disconnect();
        // 等待一下確保完全斷開
        await new Promise(resolve => setTimeout(resolve, 1000));
      }

      // 重新獲取網關信息（可能包含新的 WebSocket 配置）
      const gatewayResult = await apiService.getGateway(gateway._id, storeId);
      
      if (!gatewayResult.success || !gatewayResult.data) {
        throw new Error(gatewayResult.error || '無法獲取網關信息');
      }

      const updatedGateway = gatewayResult.data;

      // 檢查 WebSocket 配置
      if (!updatedGateway.websocket) {
        throw new Error('網關缺少 WebSocket 配置信息');
      }

      console.log('重新連接成功 - 網關資訊已更新');

      return {
        success: true,
        gateway: updatedGateway
      };

    } catch (error: any) {
      console.error('重新連接失敗:', error);

      return {
        success: false,
        error: error.message || '重新連接失敗'
      };
    } finally {
      this.isProcessing = false;
    }
  }

  /**
   * 斷開當前連接
   */
  async disconnectCurrentGateway(): Promise<void> {
    console.log('斷開當前網關連接');
    webSocketService.disconnect();
  }

  /**
   * 檢查是否正在處理
   */
  isProcessingPairing(): boolean {
    return this.isProcessing;
  }

  /**
   * 獲取當前連接狀態
   */
  getCurrentConnectionStatus() {
    return {
      isConnected: webSocketService.isWebSocketConnected(),
      connectionStatus: webSocketService.getConnectionStatus(),
      currentGateway: webSocketService.getCurrentGateway()
    };
  }

  /**
   * 測試網關配置（不實際創建）
   * @param storeId 門店ID
   * @param gatewayName 網關名稱（可選）
   */
  generateTestGatewayConfig(storeId: string, gatewayName?: string): GatewayConfig {
    return this.generateGatewayConfig(storeId, gatewayName);
  }

  /**
   * 驗證門店是否有效
   * @param storeId 門店ID
   */
  async validateStore(storeId: string): Promise<boolean> {
    try {
      const storesResult = await apiService.getStores();
      
      if (!storesResult.success || !storesResult.data) {
        return false;
      }

      const stores = storesResult.data;
      return stores.some(store => store._id === storeId || store.id === storeId);
    } catch (error) {
      console.error('驗證門店失敗:', error);
      return false;
    }
  }

  /**
   * 獲取門店的現有網關列表
   * @param storeId 門店ID
   */
  async getStoreGateways(storeId: string) {
    try {
      const result = await apiService.getGateways(storeId);
      return result;
    } catch (error: any) {
      console.error('獲取門店網關列表失敗:', error);
      return {
        success: false,
        error: error.message
      };
    }
  }

  /**
   * 檢查 MAC 地址是否已存在
   * @param macAddress MAC 地址
   * @param storeId 門店ID
   */
  async checkMacAddressExists(macAddress: string, storeId: string): Promise<boolean> {
    try {
      const gatewaysResult = await this.getStoreGateways(storeId);
      
      if (!gatewaysResult.success || !gatewaysResult.data) {
        return false;
      }

      const gateways = gatewaysResult.data;
      return gateways.some(gateway => gateway.macAddress === macAddress);
    } catch (error) {
      console.error('檢查 MAC 地址失敗:', error);
      return false;
    }
  }

  /**
   * 生成唯一的 MAC 地址（確保不重複）
   * @param storeId 門店ID
   */
  async generateUniqueMacAddress(storeId: string): Promise<string> {
    let macAddress: string;
    let attempts = 0;
    const maxAttempts = 10;

    do {
      macAddress = generateRandomMac();
      attempts++;
      
      if (attempts >= maxAttempts) {
        console.warn('生成唯一 MAC 地址達到最大嘗試次數，使用當前生成的地址');
        break;
      }
    } while (await this.checkMacAddressExists(macAddress, storeId));

    return macAddress;
  }

  /**
   * 創建帶有唯一 MAC 地址的網關配置
   * @param storeId 門店ID
   * @param gatewayName 網關名稱（可選）
   */
  async generateUniqueGatewayConfig(storeId: string, gatewayName?: string): Promise<GatewayConfig> {
    const uniqueMacAddress = await this.generateUniqueMacAddress(storeId);
    
    const config: GatewayConfig = {
      name: gatewayName || generateGatewayName(),
      macAddress: uniqueMacAddress,
      status: GATEWAY_CONSTANTS.DEFAULT_STATUS,
      model: generateGatewayModel(),
      wifiFirmwareVersion: generateFirmwareVersion(),
      btFirmwareVersion: generateFirmwareVersion(),
      ipAddress: generateRandomIp(),
      storeId: storeId,
      lastSeen: new Date()
    };

    return config;
  }
}

// 創建單例實例
export const autoPairingService = new AutoPairingService();
export default autoPairingService;
